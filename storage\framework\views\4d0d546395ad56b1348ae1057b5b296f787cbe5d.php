<?php $__env->startSection('title', 'Company'); ?>

<?php $__env->startSection('css_page'); ?>
    <!-- BEGIN VENDOR CSS-->
    <!-- END VENDOR CSS-->

    <!-- BEGIN Page Level CSS-->
    <style>
        /* width */
        ::-webkit-scrollbar {
            width: 5px;
        }

        /* Track */
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
            background: #c8c8c8;
        }

        /* Handle on hover */
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* color danger */
        .color-danger {
            color: #ff0000;
        }
        .text-danger{
            color: #F64E60 !important;
        }
    </style>
    <!-- END Page Level CSS-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="content classMasterData d-flex flex-column flex-column-fluid" style="padding-top: 10px !important; padding-bottom: 0px !important;" id="kt_content">
        <!--begin::Subheader-->
        
        <!--end::Subheader-->
        <!--begin::Entry-->
        <div class="d-flex flex-column-fluid">
            <!--begin::Container-->
            <div class="container-fluid">
                <!--begin::Notice-->
                <!--end::Notice-->
                <!--begin::Card-->
                <div class="card card-custom margin-bot-card">
                    <div class="card-header flex-wrap py-3">
                        <div class="card-title">
                            <h3 class="card-label">Data Company</h3>
                                <span class="d-block text-muted pt-2 font-size-sm"></span>
                            </h3>
                        </div>
                        <div class="card-toolbar">
                            <!--begin::Button-->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-C')): ?>
                                <button id="addMenu" name="addMenu" class="btn btn-primary font-weight-bolder">
                                    <span class="svg-icon svg-icon-md">
                                        <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                                        <!--end::Svg Icon-->
                                    </span>New Record</a>
                                </button>
                        <?php endif; ?>
                        <!--end::Button-->
                        </div>
                    </div>
                    <div class="card-body">
                        <!--begin: Search Form-->
                        <!--begin::Search Form-->
                        <div class="mb-7">
                            <div class="row align-items-center">
                                <div class="col-lg-9 col-xl-8">
                                    <div class="row align-items-center">
                                        <div class="col-md-4 my-2 my-md-0">
                                            <div class="input-icon">
                                                <input type="text" class="form-control" placeholder="Search..."
                                                       id="kt_datatable_search_query"/>
                                                <span>
                                                        <i class="flaticon2-search-1 text-muted"></i>
                                                    </span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger font-weight-bold px-6" id="btn-search">Search</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Search Form-->
                        <!--end: Search Form-->
                        <!--begin: Datatable-->
                        <div class="datatable datatable-bordered datatable-head-custom " id="kt_datatable_menu"></div>
                        <!--end: Datatable-->
                    </div>
                </div>
                <!--end::Card-->
            </div>
            <!--end::Container-->
        </div>
        <!--end::Entry-->
    </div>

    <!--begin:Modal-->
    <div class="modal fade" id="modalMenu" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalMenuTitle">Create Company</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i aria-hidden="true" class="ki ki-close"></i>
                    </button>
                </div>
                <!--begin:Form-->
                <div class="modal-body" style="height: 400px;overflow-x: hidden;">
                    <form role="form" class="form" name="formmenus" id="formmenus" enctype="multipart/formdata" method="">
                        <div class="mb-7">
                            <div class="form-group row">
                                <label class="col-lg-3 col-form-label">Company<span class="color-danger">*</span>:</label>
                                <div class="col-lg-9">
                                    <input type="text" class="form-control" id="kode_opco" name="kode_opco"
                                           placeholder="e.g: SG"/>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-lg-3 col-form-label">Description<span class="color-danger">*</span>:</label>
                                <div class="col-lg-9">
                                    <input type="text" class="form-control" id="nama_opco" name="nama_opco"
                                           placeholder="e.g: Semen Gresik"/>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-lg-3 col-form-label">Holding<span class="color-danger">*</span>:</label>
                                <div class="col-lg-9">
                                    <select class="form-control select2" name="holding" id="holding" style="width: 100%;">
                                        <option class="form-control" value='SIG'>SIG</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-lg-3 col-form-label">Reference SAP<span class="color-danger">*</span>:</label>
                                <div class="col-lg-9">
                                    <input type="text" class="form-control" name="reference_sap" id="reference_sap">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-lg-3 col-form-label">Status<span class="color-danger">*</span>:</label>
                                <div class="col-lg-9">
                                    <select class="form-control select2" name="status" id="status" style="width: 100%;">
                                        <option class="form-control" value='1' selected>Active</option>
                                        <option class="form-control" value='0'>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-light-danger font-weight-bold" data-dismiss="modal"><i
                                    class="fa fa-times"></i>Cancel
                        </button>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(['company-C' , 'company-U'])): ?>
                            <button type="submit" id="saveMenu" data-id="" class="btn btn-success font-weight-bold">
                                <i class="fa fa-save"></i> Save changes
                            </button>
                        <?php endif; ?>
                    </div>
                <!--end:Form-->
            </div>
        </div>
    </div>
    <!--end:Modal-->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_page'); ?>
    <!--begin::Page Vendors(used by this page)-->
    <!--end::Page Vendors-->
    <!--begin::Page Scripts(used by this page)-->
    <!--end::Page Scripts-->

    <script type="text/javascript">

        $(document).ready(function () {

            if (document.getElementsByClassName("classMasterData")) {
                var element = document.getElementById("kt_wrapper");
                element.classList.add("headerSync");
            }
            $('.select2').select2();

            var datatable = $('#kt_datatable_menu');

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-R')): ?>

            datatable.KTDatatable({
                // datasource definition
                data: {
                    type: 'remote',
                    source: {
                        read: {
                            url: '/company/list',
                            method: 'GET',
                        }
                    },
                    pageSize: 10,
                },
                // layout definition
                layout: {
                    scroll: false, // enable/disable datatable scroll both horizontal and vertical when needed.
                    footer: false // display/hide footer
                },
                // column sorting
                sortable: true,
                pagination: true,
                rows: {
                        autoHide: false,
                    },
                search: {
                    input: $('#kt_datatable_search_query'),
                    key: 'generalSearch',
                    onEnter: true,
                },
                // columns definition
                columns: [
                    {
                        field: 'kode_opco',
                        title: 'Company',
                        width: 100,
                    }, {
                        field: 'nama_opco',
                        title: 'Description',
                        width: 100,
                    }, {
                        field: 'holding',
                        title: 'Holding',
                        width: 100,
                    },{
                        field: 'reference_sap',
                        title: 'Reference SAP',
                    }, {
                        field: 'status',
                        title: 'Status',
                        width: 100,
                        template: function(row) {
                            var status = {
                                '1': {
                                    'title': 'Aktif (Y)',
                                    'state': 'success'
                                },
                                '0': {
                                    'title': 'Tidak Aktif (N)',
                                    'state': 'danger'
                                },
                            };
                            return `
                                <span class="label label-${status[row.status].state} label-dot mr-2"></span>
                                <span class="font-weight-bold text-${status[row.status].state}">
                                    ${status[row.status].title}
                                </span>`;
                        }
                        // template: function (row) {
                        //     var status = {
                        //         1: {'title': 'Active', 'class': ' kt-badge--success'},
                        //         0: {'title': 'Inactive', 'class': ' kt-badge--danger'},
                        //     };
                        //     return '<span class="kt-badge ' + status[row.status].class + ' kt-badge--inline kt-badge--pill">' + status[row.status].title + '</span>';
                        // }
                    }, {
                        field: 'Actions',
                        title: 'Actions',
                        sortable: false,
                        width: 70,
                        autoHide: false,
                        overflow: 'visible',
                        template: function (row) {
                            return "<center>" +
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-U')): ?>
                                        "<button type='button' class='edits btn btn-sm btn-icon btn-outline-warning ' title='Edit' data-toggle='tooltip' data-id=" + row.id + " ><i class='fa fa-edit'></i> </button>  " +
                                    <?php endif; ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-D')): ?>
                                        "<button type='button' class='deletes btn-sm btn btn-icon btn-outline-danger' title='Delete' data-toggle='tooltip' alt='' data-id=" + row.id+ " ><i class='fa fa-trash'></i></button>  " +
                                    <?php endif; ?>
                                        "</center>";
                        },
                    }
                ],

            });

            <?php endif; ?>

            $(document).on('click', '#saveMenu', function() {
                $('#formmenus').trigger('submit');
            })

            $(document).on('click', '#btn-search', function() {
                datatable.search($("#kt_datatable_search_query").val());
            })

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-C')): ?>
            $(document).on('click', '#addMenu', function () {
                $("#saveMenu").data("id", "");
                $('#modalMenuTitle').text('Create Company');
                $('#modalMenu').modal('show');
                // $("#holding").val('').trigger('change');
                $(`.form-control`).removeClass('is-invalid');
                $(`.invalid-feedback`).remove();
                let form = document.forms.formmenus; // <form name="formmenus"> element
                form.reset();
            });

            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-U')): ?>
            $(document).on('click', '.edits', function () {
                $.ajax({
                    type: 'GET', // define the type of HTTP verb we want to use (POST for our form)
                    url: './company/' + $(this).data('id'), // the url where we want to POST
                    beforeSend: function () {
                        let form = document.forms.formmenus; // <form name="formmenus"> element
                        form.reset();
                        $(`.form-control`).removeClass('is-invalid');
                        $(`.invalid-feedback`).remove();
                    }
                }).done(function (res) {
                    let form = document.forms.formmenus; // <form name="formmenus"> element
                    console.log(res.success);
                    if (res.success) {
                        showtoastr('success', res.message);
                        $(form.elements.kode_opco).val(res.data.kode_opco);
                        $(form.elements.nama_opco).val(res.data.nama_opco);
                        $(form.elements.holding).val(res.data.holding).trigger('change');
                        $(form.elements.reference_sap).val(res.data.reference_sap);
                        $(form.elements.status).val(res.data.status).trigger('change');
                        // $('#').val(res.data.company_code);
                        // $('#company_name').val(res.data.company_name);
                        // var index = $('#parent_company_code').get(res.data.company_code).selectedIndex;
                        // $('#parent_company_code option:eq(' + index + ')').remove();
                        $("#saveMenu").data( "id", res.data.id);
                    }
                }).fail(function (data) {
                    show_toastr('error', data.responseJSON.status, data.responseJSON.message);
                    $.each(data.responseJSON.errors, function (index, value) {
                        show_toastr('error', index, value);
                    });
                }).always(function () {
                    $('#modalMenuTitle').text('Edit Data Company');
                    $('#modalMenu').modal('show');
                });
            });

            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(['company-C', 'company-U'])): ?>
            $('#formmenus').submit(function (e) {
                e.preventDefault();
                var formData = new FormData($("#formmenus")[0]);
                // var formData = $('#formmenus').serializeArray(); // our data object
                var method = "POST";
                let menuID = $("#saveMenu").data("id");

                if (typeof menuID == "undefined" || menuID == "") {
                    var url = `./company`;
                } else {
                    var url = `./company/${menuID}/update`;
                }
                // var url = (menuID != "" || menuID != undefined) ? `./costcenter${menuID}/update` : `./costcenter`;

                $.ajax({
                    type: method, // define the type of HTTP verb we want to use (POST for our form)
                    url: url, // the url where we want to POST
                    data: formData,
                    dataType: 'JSON', // what type of data do we expect back from the server
                    contentType: false,
                    processData: false,
                    beforeSend: function () {
                        $(`.form-control`).removeClass('is-invalid');
                        $(`.invalid-feedback`).remove();
                        $('#saveMenu').attr('disabled', true).html("<i class='fa fa-spinner fa-spin'></i> processing");
                    }
                }).done(function (data) {
                    $("#modalMenu").modal('hide');
                    showtoastr('success', data.message);
                    $("#saveMenu").data("id", "");
                    $("#formmenus")[0].reset();
                    menuID = "";
                    let form = document.forms.formmenus; // <form name="formmenus"> element
                    form.reset();
                    datatable.reload();
                }).fail(function (data) {
                    show_toastr('error', data.responseJSON.status, data.responseJSON.message);
                    $.each(data.responseJSON.errors, function (index, value) {
                        if ($(`input[name='${index}']`)) {
                            $(`input[name='${index}']`).addClass(`is-invalid`);
                            $(`input[name='${index}']`).after(`<div class="invalid-feedback">${value}</div>`);
                        }
                        // if ($(`select[name='${index}']`)) {
                        //     $(`select[name='${index}']`).addClass(`is-invalid`);
                        //     $(`select[name='${index}']`).after(`<div class="invalid-feedback">${value}</div>`);
                        // }
                        show_toastr('error', index, value);
                    });
                }).always(function () {
                    $('#saveMenu').attr('disabled', false).html("<i class='fa fa-save'></i> Save");
                });
            });
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('company-D')): ?>
            $(document).on('click', '.deletes', function () {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                })
                    .then(isConfirm => {
                    if(isConfirm.isConfirmed
            )
                {
                    $.ajax({
                        type: 'DELETE', // define the type of HTTP verb we want to use (POST for our form)
                        url: './company/' + $(this).data('id'), // the url where we want to POST
                    })
                        .done(function (data) {
                            showtoastr('success', data.message);
                        })
                        .fail(function (data) {
                            show_toastr('error', data.responseJSON.status, data.responseJSON.message);
                            $.each(data.responseJSON.messages, function (index, value) {
                                show_toastr('error', index, value);
                            });
                        })
                        .always(function () {
                            datatable.reload();
                        });
                }
            })
                ;
            });
            <?php endif; ?>

        });


    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\dev-dmm\resources\views/company.blade.php ENDPATH**/ ?>