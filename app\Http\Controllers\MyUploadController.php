<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Document;
use App\Models\Menu;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use function response;
use function responseFail;
use function responseSuccess;
use function trans;
use function view;

class MyUploadController extends Controller
{

    public function index()
    {
        $data = [
            'title' => 'My Upload',
            'breadcrumb' => [
                [
                    'title' => 'Master Data',
                    'url' => '/my-upload',
                ],
                [
                    'title' => 'My Upload',
                    'url' => '',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu'] = Menu::select('id', 'name')->get();
        $data['categories'] = Category::getTree();

        return view('myUpload', $data);
    }

    public function datatables(Request $request)
    {
        $query = Document::select('t_document.*', 'm_category.name as category_name')
                ->leftjoin('m_category', 'm_category.id', 't_document.category_id')
                ->orderBy('id')->get();
        $data = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  Request  $request
     * @return Response
     */
    public function store(Request $request)
    {
        $rules = [
            'title' => 'required',
            'category_id' => 'required',
            'file' => 'required|file|mimes:pdf,jpg,jpeg,mp4|max:51200',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique' => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);
        try {
            $filename = $request->file('file')->getBasename();
            $path = $request->file('file')->store('documents', 'public');
            $doc = Document::create([
                    'title' => $request->title,
                    'description' => $request->description,
                    'filename' => $filename,
                    'path' => $path,
                    'status' => 'Waiting',
                    'category_id' => $request->category_id,
            ]);
            $response = responseSuccess(trans('messages.create-success'), $doc);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id)
    {
        $query = Document::select('t_document.*', 'm_category.name as category_name')
            ->leftjoin('m_category', 'm_category.id', 't_document.category_id')
            ->find($id);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  Request  $request
     * @param  int  $id
     * @return Response
     */
    public function update($id, Request $request)
    {
        $data = $this->findDataWhere(Document::class, ['id' => $id]);

        $rules = [
            'title' => 'required',
            'category_id' => 'required',
            'file' => 'file|mimes:pdf,jpg,jpeg,mp4|max:51200',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique' => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);

        DB::beginTransaction();
        try {
            if ($request->hasFile('file')) {
                $filename = $request->file('file')->getBasename();
                $path = $request->file('file')->store('documents', 'public');
                $data->update([
                    'title' => $request->title,
                    'description' => $request->description,
                    'filename' => $filename,
                    'path' => $path,
                    'category_id' => $request->category_id,
                ]);
            } else {
                $data->update([
                    'title' => $request->title,
                    'description' => $request->description,
                    'category_id' => $request->category_id,
                ]);
            }
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($id)
    {
        Document::destroy($id);
        $response = responseSuccess(trans('messages.delete-success'));
        return response()->json($response, 200);
    }

    public function approve($id)
    {
        $userId = auth()->user()->id;
        $data = $this->findDataWhere(Document::class, ['id' => $id]);
        $data->update([
            'status' => 'Approved',
            'approved_by' => $userId,
            'approved_at' => date('Y-m-d H:i:s'),
        ]);
        $response = responseSuccess(trans("messages.update-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function reject($id)
    {
        $userId = auth()->user()->id;
        $data = $this->findDataWhere(Document::class, ['id' => $id]);
        $data->update([
            'status' => 'Rejected',
            'approved_by' => $userId,
            'approved_at' => date('Y-m-d H:i:s'),
        ]);
        $response = responseSuccess(trans("messages.update-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function preview($id)
    {
        $data = $this->findDataWhere(Document::class, ['id' => $id]);
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
}
